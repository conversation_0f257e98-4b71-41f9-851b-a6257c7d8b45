/**
 * MEDYA Eduarte Móveis - Slideshow com Suporte Completo ao Editor Elementor
 */
(function ($) {
    'use strict';

    const MAX_ATTEMPTS = 10;
    const RETRY_DELAY = 300;

    function initializeSlideshow(element, attempt = 1) {
        const $slideshow = $(element);
        const elementId = $slideshow.attr('class') || 'unknown';

        if (!$slideshow.length || $slideshow.data('initialized')) {
            return;
        }

        // Verifica se já tentamos muitas vezes
        if (attempt > MAX_ATTEMPTS) {
            console.warn('MEDYA Slideshow: Máximo de tentativas atingido para', elementId);
            return;
        }

        // Aguarda Splide estar disponível
        if (typeof window.Splide === 'undefined') {
            setTimeout(() => initializeSlideshow(element, attempt + 1), RETRY_DELAY);
            return;
        }

        // Verifica se a estrutura DOM está presente e válida
        const track = $slideshow.find('.splide__track');
        const list = $slideshow.find('.splide__list');
        const slides = $slideshow.find('.splide__slide');

        if (!track.length || !list.length || !slides.length) {
            console.warn(`MEDYA Slideshow: Estrutura incompleta (tentativa ${attempt}/${MAX_ATTEMPTS}), aguardando...`);
            setTimeout(() => initializeSlideshow(element, attempt + 1), RETRY_DELAY);
            return;
        }

        // Verifica se o elemento está visível no DOM
        if (!$slideshow.is(':visible') && $slideshow.closest('body').length === 0) {
            setTimeout(() => initializeSlideshow(element, attempt + 1), RETRY_DELAY);
            return;
        }

        try {
            const settings = JSON.parse($slideshow.attr('data-settings') || '{}');

            const options = {
                type: settings.effect === 'fade' ? 'fade' : 'loop',
                autoplay: settings.autoplay !== false,
                interval: settings.duration || 5000,
                rewind: settings.loop !== false,
                speed: settings.speed || 400,
                arrows: false,
                pagination: false,
                drag: true,
                // Configurações específicas para o editor
                waitForTransition: false,
                updateOnMove: true
            };

            // Desabilita autoplay no editor do Elementor
            const isElementorEditor = $('body').hasClass('elementor-editor-active') ||
                window.location.href.includes('elementor-preview');

            if (isElementorEditor) {
                options.autoplay = false;
            }

            const splide = new window.Splide(element, options);

            // Event listeners para debug
            splide.on('mounted', () => {
                console.log('MEDYA Slideshow: Montado com sucesso');
                $slideshow.data('initialized', true);
            });

            splide.on('move', (newIndex) => {
                console.log('MEDYA Slideshow: Movido para slide', newIndex);
            });

            splide.mount();

        } catch (error) {
            console.error('MEDYA Slideshow error:', error);

            // Se falhou, tenta novamente após um delay maior
            if (attempt < MAX_ATTEMPTS) {
                setTimeout(() => initializeSlideshow(element, attempt + 1), RETRY_DELAY * 2);
            }
        }
    }

    function initAllSlideshows() {
        $('.medya-eduarte-slideshow').each(function () {
            if (!$(this).data('initialized')) {
                initializeSlideshow(this);
            }
        });
    }

    // Inicialização para frontend
    $(document).ready(() => {
        setTimeout(initAllSlideshows, 500);
    });

    // Inicialização específica para o editor do Elementor
    $(window).on('elementor/frontend/init', () => {
        console.log('MEDYA Slideshow: Elementor frontend inicializado');

        // Aguarda um pouco mais no editor
        setTimeout(initAllSlideshows, 1000);

        // Observer para novos elementos adicionados dinamicamente
        if (typeof MutationObserver !== 'undefined') {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1) { // Element node
                            const $node = $(node);
                            const slideshows = $node.find('.medya-eduarte-slideshow').addBack('.medya-eduarte-slideshow');

                            slideshows.each(function () {
                                if (!$(this).data('initialized')) {
                                    setTimeout(() => initializeSlideshow(this), 500);
                                }
                            });
                        }
                    });
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    });

    // Fallback adicional para casos específicos
    $(window).on('load', () => {
        setTimeout(initAllSlideshows, 1000);
    });

    // Re-inicialização quando o Elementor atualiza o preview
    $(document).on('elementor/render/widget', () => {
        setTimeout(initAllSlideshows, 800);
    });

})(jQuery);