/**
 * MEDYA Eduarte Móveis - Slideshow JavaScript Nativo
 */
(function () {
    'use strict';

    const MAX_ATTEMPTS = 10;
    const RETRY_DELAY = 300;
    const initialized = new WeakSet();

    /**
     * Utilitários para substituir jQuery
     */
    const utils = {
        ready: function (callback) {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', callback);
            } else {
                callback();
            }
        },

        hasClass: function (element, className) {
            return element.classList.contains(className);
        },

        isVisible: function (element) {
            return element.offsetParent !== null;
        },

        isInDOM: function (element) {
            return document.body.contains(element);
        },

        querySelectorAll: function (selector, context = document) {
            return Array.from(context.querySelectorAll(selector));
        },

        querySelector: function (selector, context = document) {
            return context.querySelector(selector);
        }
    };

    /**
     * Detecta se estamos no editor do Elementor
     */
    function isElementorEditor() {
        return utils.hasClass(document.body, 'elementor-editor-active') ||
            window.location.href.includes('elementor-preview') ||
            window.location.href.includes('elementor-editor');
    }

    /**
     * Gera um seletor único para o elemento
     */
    function generateUniqueSelector(element) {
        // Tenta usar data-id do Elementor primeiro
        const elementorWidget = element.closest('[data-id]');
        if (elementorWidget) {
            const dataId = elementorWidget.getAttribute('data-id');
            return `[data-id="${dataId}"] .medya-eduarte-slideshow`;
        }

        // Fallback: usa classes únicas
        const classes = Array.from(element.classList).join('.');
        if (classes) {
            return `.${classes}`;
        }

        // Último recurso: usa posição no DOM
        const slideshows = utils.querySelectorAll('.medya-eduarte-slideshow');
        const index = slideshows.indexOf(element);
        return `.medya-eduarte-slideshow:nth-of-type(${index + 1})`;
    }

    /**
     * Inicializa um slideshow individual
     */
    function initializeSlideshow(element, attempt = 1) {
        // Verifica se já foi inicializado
        if (initialized.has(element)) {
            return;
        }

        // Verifica se já tentamos muitas vezes
        if (attempt > MAX_ATTEMPTS) {
            console.warn('MEDYA Slideshow: Máximo de tentativas atingido para', element.className);
            return;
        }

        // Aguarda Splide estar disponível
        if (typeof window.Splide === 'undefined') {
            setTimeout(() => initializeSlideshow(element, attempt + 1), RETRY_DELAY);
            return;
        }

        // Verifica se o elemento está no DOM
        if (!utils.isInDOM(element)) {
            setTimeout(() => initializeSlideshow(element, attempt + 1), RETRY_DELAY);
            return;
        }

        // IMPORTANTE: Não interagir com o DOM do elemento antes da inicialização do Splide
        // Isso pode causar o erro "[splide] A track/list element is missing" no Chrome + Elementor

        try {
            // Parse das configurações ANTES de verificar a estrutura
            const settingsAttr = element.getAttribute('data-settings');
            const settings = settingsAttr ? JSON.parse(settingsAttr) : {};

            const options = {
                type: settings.effect === 'fade' ? 'fade' : 'loop',
                autoplay: settings.autoplay !== false && !isElementorEditor(),
                interval: settings.duration || 5000,
                rewind: settings.loop !== false,
                speed: settings.speed || 400,
                arrows: false,
                pagination: false,
                drag: true,
                waitForTransition: false,
                updateOnMove: true
            };

            // SOLUÇÃO: Usar seletor de string em vez do elemento DOM direto
            // Isso resolve o bug do Chrome + Elementor
            const selector = generateUniqueSelector(element);

            console.log('MEDYA Slideshow: Inicializando com seletor:', selector);

            // Cria e monta o Splide usando o seletor de string
            const splide = new window.Splide(selector, options);

            // Event listeners
            splide.on('mounted', () => {
                console.log('MEDYA Slideshow: Montado com sucesso');
                initialized.add(element);
            });

            splide.on('move', (newIndex) => {
                console.log('MEDYA Slideshow: Movido para slide', newIndex);
            });

            // AGORA podemos verificar a estrutura APÓS a inicialização
            splide.on('ready', () => {
                const track = utils.querySelector('.splide__track', element);
                const list = utils.querySelector('.splide__list', element);
                const slides = utils.querySelectorAll('.splide__slide', element);

                if (!track || !list || slides.length === 0) {
                    console.warn('MEDYA Slideshow: Estrutura incompleta detectada após montagem');
                }
            });

            splide.mount();

        } catch (error) {
            console.error('MEDYA Slideshow error:', error);

            // Se falhou, tenta novamente após um delay maior
            if (attempt < MAX_ATTEMPTS) {
                setTimeout(() => initializeSlideshow(element, attempt + 1), RETRY_DELAY * 2);
            }
        }
    }

    /**
     * Inicializa todos os slideshows na página
     */
    function initAllSlideshows() {
        const slideshows = utils.querySelectorAll('.medya-eduarte-slideshow');

        slideshows.forEach(slideshow => {
            if (!initialized.has(slideshow)) {
                initializeSlideshow(slideshow);
            }
        });
    }

    /**
     * Observer para elementos adicionados dinamicamente
     */
    function setupMutationObserver() {
        if (typeof MutationObserver === 'undefined') {
            return;
        }

        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // Element node
                        // Verifica se o próprio node é um slideshow
                        if (node.classList && node.classList.contains('medya-eduarte-slideshow')) {
                            setTimeout(() => initializeSlideshow(node), 500);
                        }

                        // Verifica se há slideshows dentro do node
                        const slideshows = utils.querySelectorAll('.medya-eduarte-slideshow', node);
                        slideshows.forEach(slideshow => {
                            if (!initialized.has(slideshow)) {
                                setTimeout(() => initializeSlideshow(slideshow), 500);
                            }
                        });
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    /**
     * Inicialização principal
     */
    function init() {
        console.log('MEDYA Slideshow: Inicializando...');

        // Inicialização básica
        setTimeout(initAllSlideshows, 500);

        // Setup do observer para elementos dinâmicos
        setupMutationObserver();

        // Inicialização específica para Elementor
        if (isElementorEditor()) {
            console.log('MEDYA Slideshow: Modo editor detectado');

            // Aguarda mais tempo no editor
            setTimeout(initAllSlideshows, 1500);

            // Re-inicialização quando o Elementor atualiza
            document.addEventListener('elementor/render/widget', () => {
                setTimeout(initAllSlideshows, 800);
            });
        }
    }

    // Inicialização quando o DOM estiver pronto
    utils.ready(init);

    // Fallback para window.load
    window.addEventListener('load', () => {
        setTimeout(initAllSlideshows, 1000);
    });

    // Inicialização específica para o frontend do Elementor
    window.addEventListener('elementor/frontend/init', () => {
        console.log('MEDYA Slideshow: Elementor frontend inicializado');
        setTimeout(initAllSlideshows, 1000);
    });

})();