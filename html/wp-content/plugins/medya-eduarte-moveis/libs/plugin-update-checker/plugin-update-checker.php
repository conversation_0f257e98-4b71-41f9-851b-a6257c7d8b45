<?php
/*
 * Plugin Update Checker Placeholder
 *
 * Este é um arquivo placeholder para o Plugin Update Checker.
 * Em produção, você deve substituir este diretório pela biblioteca real
 * do Plugin Update Checker disponível em:
 * https://github.com/YahnisElsts/plugin-update-checker
 */

// Namespace placeholder para evitar erros
namespace YahnisElsts\PluginUpdateChecker\v5 {

    if (!defined('ABSPATH')) {
        exit;
    }

    class PucFactory
    {
        public static function buildUpdateChecker($metadataUrl, $pluginFile, $slug = null, $checkPeriod = 12, $optionName = '')
        {
            // Placeholder - não faz nada em desenvolvimento
            return new class {
                public function __call($name, $arguments)
                {
                    // Método vazio para evitar erros
                }
            };
        }
    }
    // Aviso para desenvolvedores
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('MEDYA Eduarte Móveis: Plugin Update Checker placeholder ativo. Substitua pela biblioteca real em produção.');
    }
}
