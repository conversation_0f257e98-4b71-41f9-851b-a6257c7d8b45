<?php

/**
 * Arquivo de desinstalação do MEDYA Eduarte Móveis
 * 
 * Este arquivo é executado quando o plugin é desinstalado
 * (não apenas desativado) pelo WordPress.
 */

// Se a desinstalação não foi chamada pelo WordPress, saia
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Verifica se é realmente uma desinstalação
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Remove todas as opções do plugin
 */
function medya_eduarte_remove_options()
{
    // Opções principais do plugin
    delete_option('medya_eduarte_sections');
    delete_option('medya_eduarte_post_type');

    // Remove opções de metadados das seções (se existirem)
    global $wpdb;

    $wpdb->query(
        $wpdb->prepare(
            "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
            'medya_section_meta_%'
        )
    );

    // Remove estatísticas de visualização
    delete_option('medya_eduarte_section_views');
}

/**
 * Remove um grupo de cache
 */
function wp_cache_delete_group($group)
{
    /** @type WP_Object_Cache $wp_object_cache */
    global $wp_object_cache;

    $cache = $wp_object_cache->cache;

    if (isset($cache[$group])) {
        unset($cache[$group]);

        $wp_object_cache->cache = $cache;
    }
}

/**
 * Remove dados de cache relacionados
 */
function medya_eduarte_clear_cache()
{
    // Limpa cache do WordPress
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
    }

    // Limpa cache de objeto se disponível
    if (function_exists('wp_cache_delete_group')) {
        wp_cache_delete_group('medya_eduarte');
    }
}

/**
 * Remove transients relacionados
 */
function medya_eduarte_remove_transients()
{
    global $wpdb;

    // Remove transients que começam com medya_eduarte_
    $wpdb->query(
        $wpdb->prepare(
            "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s",
            '_transient_medya_eduarte_%',
            '_transient_timeout_medya_eduarte_%'
        )
    );
}

/**
 * Log da desinstalação
 */
function medya_eduarte_log_uninstall()
{
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('MEDYA Eduarte Móveis: Plugin desinstalado e dados removidos');
    }
}

// Executa a limpeza apenas se for uma desinstalação real
if (defined('WP_UNINSTALL_PLUGIN')) {
    // Remove opções
    medya_eduarte_remove_options();

    // Limpa cache
    medya_eduarte_clear_cache();

    // Remove transients
    medya_eduarte_remove_transients();

    // Log da desinstalação
    medya_eduarte_log_uninstall();

    // Flush rewrite rules uma última vez
    flush_rewrite_rules();
}

// Nota: Não removemos posts ou attachments criados pelo usuário
// pois eles podem ser importantes para o site mesmo sem o plugin
