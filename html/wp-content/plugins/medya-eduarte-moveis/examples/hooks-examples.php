<?php
/**
 * Exemplos de uso dos hooks e filtros do MEDYA Eduarte Móveis
 * 
 * Este arquivo contém exemplos de como usar os hooks e filtros
 * disponíveis no plugin. Adicione estes códigos ao functions.php
 * do seu tema ou em um plugin personalizado.
 */

// Não execute este arquivo diretamente
if (!defined('ABSPATH')) {
    exit;
}

/**
 * FILTROS DISPONÍVEIS
 */

// 1. Modificar opções de seções no dropdown do widget
add_filter('medya_eduarte_sections_options', function($options) {
    // Adiciona uma opção personalizada
    $options['custom_section'] = 'Seção Personalizada';
    
    // Remove uma seção específica
    unset($options['section_1']);
    
    return $options;
});

// 2. Modificar imagens de uma seção antes de exibir
add_filter('medya_eduarte_section_images', function($images, $section_id) {
    // Adiciona uma marca d'água a todas as imagens
    foreach ($images as &$image) {
        // Exemplo: adiciona parâmetro para redimensionamento
        $image['url'] = add_query_arg(['watermark' => '1'], $image['url']);
    }
    
    // Limita a 5 imagens por seção
    if (count($images) > 5) {
        $images = array_slice($images, 0, 5);
    }
    
    return $images;
}, 10, 2);

// 3. Modificar configurações do Swiper (adicione este filtro no JavaScript)
/*
jQuery(document).ready(function($) {
    // Este seria implementado no slideshow.js
    $(document).on('medya_eduarte_swiper_config', function(event, config, settings) {
        // Adiciona navegação por setas
        config.navigation = {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        };
        
        // Adiciona paginação
        config.pagination = {
            el: '.swiper-pagination',
            clickable: true,
        };
        
        return config;
    });
});
*/

/**
 * ACTIONS DISPONÍVEIS
 */

// 1. Executar ação após salvar seções
add_action('medya_eduarte_sections_saved', function($sections) {
    // Log das alterações
    error_log('MEDYA Eduarte: Seções atualizadas - ' . count($sections) . ' seções');
    
    // Limpar cache se necessário
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
    }
    
    // Enviar notificação por email (exemplo)
    $admin_email = get_option('admin_email');
    wp_mail(
        $admin_email,
        'Seções Eduarte Atualizadas',
        'As seções de galeria foram atualizadas no site.'
    );
});

// 2. Executar ação antes de renderizar slideshow
add_action('medya_eduarte_before_slideshow', function($section_id, $images) {
    // Registra visualização da seção
    $views = get_option('medya_eduarte_section_views', []);
    $views[$section_id] = ($views[$section_id] ?? 0) + 1;
    update_option('medya_eduarte_section_views', $views);
    
    // Adiciona dados estruturados para SEO
    if (!empty($images)) {
        echo '<script type="application/ld+json">';
        echo json_encode([
            '@context' => 'https://schema.org',
            '@type' => 'ImageGallery',
            'name' => 'Galeria Eduarte - ' . $section_id,
            'image' => array_column($images, 'url')
        ]);
        echo '</script>';
    }
}, 10, 2);

// 3. Executar ação após renderizar slideshow
add_action('medya_eduarte_after_slideshow', function($section_id, $images) {
    // Adiciona botões de navegação personalizados
    echo '<div class="medya-custom-navigation">';
    echo '<button class="medya-prev-btn">‹ Anterior</button>';
    echo '<button class="medya-next-btn">Próximo ›</button>';
    echo '</div>';
    
    // Adiciona contador de imagens
    $count = count($images);
    echo '<div class="medya-image-counter">';
    echo '<span class="current">1</span> / <span class="total">' . $count . '</span>';
    echo '</div>';
}, 10, 2);

/**
 * EXEMPLOS DE CUSTOMIZAÇÃO AVANÇADA
 */

// Adicionar campo personalizado às seções
add_action('medya_eduarte_sections_saved', function($sections) {
    // Salva metadados adicionais para cada seção
    foreach ($sections as $section) {
        $meta_key = 'medya_section_meta_' . $section['id'];
        update_option($meta_key, [
            'created_at' => current_time('mysql'),
            'created_by' => get_current_user_id(),
            'last_modified' => current_time('mysql')
        ]);
    }
});

// Integração com outros plugins
add_filter('medya_eduarte_section_images', function($images, $section_id) {
    // Integração com plugin de otimização de imagens
    if (function_exists('wp_smush_attachment')) {
        foreach ($images as &$image) {
            // Otimiza imagem se necessário
            wp_smush_attachment($image['id']);
        }
    }
    
    // Integração com plugin de lazy loading
    if (class_exists('LazyLoad')) {
        foreach ($images as &$image) {
            $image['lazy'] = true;
        }
    }
    
    return $images;
}, 10, 2);

// Adicionar estilos personalizados baseados na seção
add_action('medya_eduarte_before_slideshow', function($section_id, $images) {
    // CSS específico por seção
    $custom_css = '';
    
    switch ($section_id) {
        case 'section_1':
            $custom_css = '.medya-eduarte-slideshow { border-radius: 20px; }';
            break;
        case 'section_2':
            $custom_css = '.medya-eduarte-slideshow { filter: sepia(20%); }';
            break;
    }
    
    if ($custom_css) {
        echo '<style>' . $custom_css . '</style>';
    }
}, 10, 2);

// Adicionar dados de analytics
add_action('medya_eduarte_after_slideshow', function($section_id, $images) {
    // Google Analytics event tracking
    ?>
    <script>
    if (typeof gtag !== 'undefined') {
        gtag('event', 'slideshow_view', {
            'section_id': '<?php echo esc_js($section_id); ?>',
            'image_count': <?php echo count($images); ?>
        });
    }
    </script>
    <?php
}, 10, 2);

/**
 * SHORTCODE PERSONALIZADO
 */

// Cria shortcode para usar fora do Elementor
add_shortcode('eduarte_slideshow', function($atts) {
    $atts = shortcode_atts([
        'section' => '',
        'height' => '400px',
        'autoplay' => 'true',
        'loop' => 'true'
    ], $atts);
    
    if (empty($atts['section'])) {
        return '<p>Seção não especificada.</p>';
    }
    
    $images = Medya_Eduarte_Moveis::get_section_images($atts['section']);
    
    if (empty($images)) {
        return '<p>Nenhuma imagem encontrada.</p>';
    }
    
    ob_start();
    ?>
    <div class="medya-eduarte-slideshow-shortcode" 
         style="height: <?php echo esc_attr($atts['height']); ?>"
         data-autoplay="<?php echo esc_attr($atts['autoplay']); ?>"
         data-loop="<?php echo esc_attr($atts['loop']); ?>">
        <?php foreach ($images as $image): ?>
            <div class="slide" style="background-image: url('<?php echo esc_url($image['url']); ?>')"></div>
        <?php endforeach; ?>
    </div>
    <?php
    return ob_get_clean();
});

/**
 * WIDGET DASHBOARD PERSONALIZADO
 */

// Adiciona widget no dashboard com estatísticas
add_action('wp_dashboard_setup', function() {
    wp_add_dashboard_widget(
        'medya_eduarte_stats',
        'Estatísticas Eduarte Móveis',
        function() {
            $sections = Medya_Eduarte_Moveis::get_sections();
            $views = get_option('medya_eduarte_section_views', []);
            
            echo '<h4>Seções Configuradas: ' . count($sections) . '</h4>';
            
            if (!empty($views)) {
                echo '<h4>Visualizações por Seção:</h4>';
                echo '<ul>';
                foreach ($views as $section_id => $count) {
                    $section_name = 'Seção ' . $section_id;
                    foreach ($sections as $section) {
                        if ($section['id'] === $section_id) {
                            $section_name = $section['name'];
                            break;
                        }
                    }
                    echo '<li>' . esc_html($section_name) . ': ' . intval($count) . ' visualizações</li>';
                }
                echo '</ul>';
            }
        }
    );
});
